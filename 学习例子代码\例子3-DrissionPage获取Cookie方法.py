#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
DrissionPage Cookie获取学习示例：使用DrissionPage获取网站Cookie
功能：连接浏览器、访问网站、获取Cookie并保存
重点：学习DrissionPage的cookies()方法的使用
"""

from DrissionPage import Chromium
import json
import time
from datetime import datetime

def get_cookies_basic_example():
    """
    基础Cookie获取示例
    演示如何使用DrissionPage获取网站的Cookie
    """
    print("=" * 80)
    print("📚 DrissionPage Cookie获取基础示例")
    print("🎯 目标：获取闲鱼网站的基础Cookie")
    print("=" * 80)
    
    try:
        # 步骤1：连接到浏览器
        print("🚀 第一步：连接到浏览器")
        browser = Chromium(9222)  # 连接到9222端口的浏览器
        print("✅ 已连接到浏览器")
        
        # 步骤2：新建独立标签页
        print("\n🚀 第二步：新建独立标签页")
        # 总是新建标签页，不影响现有标签页
        print("📄 正在新建独立的学习标签页...")
        tab = browser.new_tab()
        print("✅ 新建标签页成功，不会影响现有标签页")
        
        # 激活标签页（显示在前台）
        tab.set.activate()
        print("🎯 标签页已激活")
        
        # 步骤3：访问目标网站
        print("\n🚀 第三步：访问目标网站")
        target_url = "https://2.taobao.com/"
        print(f"🌐 正在访问：{target_url}")
        
        # 使用get()方法访问网站
        success = tab.get(target_url)
        
        if success:
            print("✅ 网站访问成功")
        else:
            print("❌ 网站访问失败")
            return None
        
        # 等待页面完全加载
        print("⏳ 等待页面加载完成...")
        tab.wait.doc_loaded()  # 等待文档加载完成
        print("✅ 页面加载完成")
        
        # 步骤4：获取Cookie - 这是重点！
        print("\n🚀 第四步：获取Cookie（重点学习）")
        print("🍪 正在获取Cookie...")
        
        # 方法1：获取当前域名的Cookie（默认方式）
        print("\n📋 方法1：获取当前域名的Cookie")
        current_domain_cookies = tab.cookies()  # 默认只获取当前域名的Cookie
        print(f"   获取到 {len(current_domain_cookies)} 个当前域名的Cookie")
        
        # 方法2：获取所有域名的Cookie
        print("\n📋 方法2：获取所有域名的Cookie")
        all_domain_cookies = tab.cookies(all_domains=True)  # 获取所有域名的Cookie
        print(f"   获取到 {len(all_domain_cookies)} 个所有域名的Cookie")
        
        # 方法3：获取详细信息的Cookie
        print("\n📋 方法3：获取详细信息的Cookie")
        detailed_cookies = tab.cookies(all_domains=True, all_info=True)  # 获取包含所有信息的Cookie
        print(f"   获取到 {len(detailed_cookies)} 个详细信息的Cookie")
        
        # 步骤5：Cookie格式转换 - 这也是重点！
        print("\n🚀 第五步：Cookie格式转换（重点学习）")
        
        # 转换为字符串格式（用于HTTP请求头）
        print("\n📝 转换为字符串格式：")
        cookie_string = all_domain_cookies.as_str()
        print(f"   字符串长度：{len(cookie_string)} 字符")
        print(f"   字符串格式示例：{cookie_string[:100]}...")  # 只显示前100个字符
        
        # 转换为字典格式
        print("\n📝 转换为字典格式：")
        cookie_dict = all_domain_cookies.as_dict()
        print(f"   字典包含 {len(cookie_dict)} 个键值对")
        print("   字典格式示例：")
        # 显示前5个键值对作为示例
        for i, (key, value) in enumerate(cookie_dict.items()):
            if i < 5:  # 只显示前5个
                print(f"      {key}: {value}")
            else:
                print(f"      ... 还有 {len(cookie_dict) - 5} 个")
                break
        
        # 转换为JSON格式
        print("\n📝 转换为JSON格式：")
        cookie_json = all_domain_cookies.as_json()
        print(f"   JSON字符串长度：{len(cookie_json)} 字符")
        
        # 步骤6：检查重要的Cookie字段
        print("\n🚀 第六步：检查重要的Cookie字段")
        important_cookies = ['_m_h5_tk', 'cookie2', '_tb_token_', 'sgcookie']
        found_important = []
        
        for cookie_name in important_cookies:
            if f'{cookie_name}=' in cookie_string:
                found_important.append(cookie_name)
                print(f"   ✅ 找到重要Cookie: {cookie_name}")
            else:
                print(f"   ❌ 未找到Cookie: {cookie_name}")
        
        if found_important:
            print(f"\n✅ 总共找到 {len(found_important)} 个重要Cookie字段")
        else:
            print("\n⚠️ 未找到任何重要Cookie字段，可能需要登录")
        
        # 步骤7：保存Cookie到文件
        print("\n🚀 第七步：保存Cookie到文件")
        save_cookie_to_file(cookie_string, cookie_dict, detailed_cookies)
        
        print("\n🎉 Cookie获取学习完成！")
        print("📋 学习总结：")
        print("   ✅ 学会了使用tab.cookies()方法获取Cookie")
        print("   ✅ 学会了Cookie的三种格式转换：as_str()、as_dict()、as_json()")
        print("   ✅ 学会了检查重要Cookie字段")
        print("   ✅ 学会了保存Cookie到文件")
        
        return cookie_string
        
    except Exception as e:
        print(f"❌ Cookie获取过程中发生错误：{e}")
        return None

def save_cookie_to_file(cookie_string, cookie_dict, detailed_cookies):
    """
    将Cookie保存到文件的示例
    演示如何将不同格式的Cookie保存到文件中
    """
    print("💾 正在保存Cookie到文件...")
    
    # 创建保存目录
    import os
    save_dir = "Cookie数据"
    if not os.path.exists(save_dir):
        os.makedirs(save_dir)
        print(f"📁 创建保存目录：{save_dir}")
    
    # 生成带时间戳的文件名
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    
    try:
        # 保存字符串格式的Cookie
        string_file = os.path.join(save_dir, f"cookie_string_{timestamp}.txt")
        with open(string_file, 'w', encoding='utf-8') as f:
            f.write(cookie_string)
        print(f"   ✅ 字符串格式Cookie已保存：{string_file}")
        
        # 保存字典格式的Cookie
        dict_file = os.path.join(save_dir, f"cookie_dict_{timestamp}.json")
        with open(dict_file, 'w', encoding='utf-8') as f:
            json.dump(cookie_dict, f, indent=4, ensure_ascii=False)
        print(f"   ✅ 字典格式Cookie已保存：{dict_file}")
        
        # 保存详细信息的Cookie
        detailed_file = os.path.join(save_dir, f"cookie_detailed_{timestamp}.json")
        # 将CookiesList转换为可序列化的格式
        detailed_data = []
        for cookie in detailed_cookies:
            detailed_data.append(dict(cookie))  # 转换为字典
        
        with open(detailed_file, 'w', encoding='utf-8') as f:
            json.dump(detailed_data, f, indent=4, ensure_ascii=False)
        print(f"   ✅ 详细信息Cookie已保存：{detailed_file}")
        
        print("💾 所有Cookie文件保存完成！")
        
    except Exception as e:
        print(f"❌ 保存Cookie文件时出错：{e}")

def get_cookies_with_login_example():
    """
    需要登录的Cookie获取示例
    演示如何在需要登录的情况下获取完整的Cookie
    """
    print("\n" + "=" * 80)
    print("📚 DrissionPage 登录Cookie获取示例")
    print("🎯 目标：获取登录后的完整Cookie")
    print("=" * 80)
    
    try:
        # 连接浏览器
        browser = Chromium(9222)
        # 新建独立标签页用于登录Cookie获取
        print("📄 正在新建独立的登录标签页...")
        tab = browser.new_tab()
        print("✅ 新建登录标签页成功，不会影响现有标签页")
        tab.set.activate()
        
        # 访问登录页面
        print("🌐 正在访问登录页面...")
        tab.get("https://login.taobao.com/")
        
        print("⚠️ 请在浏览器中完成登录操作...")
        print("💡 登录完成后，按回车键继续...")
        input("按回车键继续...")
        
        # 获取登录后的Cookie
        print("🍪 正在获取登录后的Cookie...")
        logged_in_cookies = tab.cookies(all_domains=True)
        cookie_string = logged_in_cookies.as_str()
        
        print(f"✅ 获取到登录后的Cookie，长度：{len(cookie_string)} 字符")
        
        # 检查登录相关的Cookie
        login_cookies = ['unb', 'uc1', 'uc3', 'uc4', '_tb_token_']
        found_login = []
        
        for cookie_name in login_cookies:
            if f'{cookie_name}=' in cookie_string:
                found_login.append(cookie_name)
                print(f"   ✅ 找到登录Cookie: {cookie_name}")
        
        if found_login:
            print(f"✅ 登录成功！找到 {len(found_login)} 个登录相关Cookie")
            
            # 保存登录后的Cookie
            save_cookie_to_file(cookie_string, logged_in_cookies.as_dict(), logged_in_cookies)
            return cookie_string
        else:
            print("⚠️ 未检测到登录状态，请确认是否已成功登录")
            return None
            
    except Exception as e:
        print(f"❌ 登录Cookie获取失败：{e}")
        return None

if __name__ == "__main__":
    """
    主程序：执行Cookie获取学习示例
    """
    print("=" * 80)
    print("📚 DrissionPage Cookie获取完整学习教程")
    print("🎯 学习目标：掌握DrissionPage的Cookie获取和处理方法")
    print("=" * 80)
    
    try:
        # 示例1：基础Cookie获取
        print("🚀 开始基础Cookie获取示例")
        basic_cookie = get_cookies_basic_example()
        
        if basic_cookie:
            print("\n✅ 基础Cookie获取示例完成！")
            
            # 询问是否继续登录示例
            print("\n" + "=" * 50)
            choice = input("是否继续学习登录Cookie获取示例？(y/n): ").lower().strip()
            
            if choice == 'y' or choice == 'yes':
                # 示例2：登录Cookie获取
                print("\n🚀 开始登录Cookie获取示例")
                login_cookie = get_cookies_with_login_example()
                
                if login_cookie:
                    print("\n✅ 登录Cookie获取示例完成！")
                else:
                    print("\n⚠️ 登录Cookie获取示例未完成")
            else:
                print("📚 跳过登录Cookie获取示例")
        else:
            print("\n❌ 基础Cookie获取示例失败")
        
    except Exception as e:
        print(f"❌ 程序执行过程中发生错误：{e}")
        print("💡 请检查：")
        print("   1. DrissionPage库是否正确安装")
        print("   2. 浏览器是否可以正常启动")
        print("   3. 网络连接是否正常")
    
    print("\n" + "=" * 80)
    print("📚 DrissionPage Cookie获取学习完成")
    print("💡 重点回顾：")
    print("   1. 使用tab.cookies()获取Cookie")
    print("   2. 使用all_domains=True获取所有域名Cookie")
    print("   3. 使用as_str()、as_dict()、as_json()转换格式")
    print("   4. 检查重要Cookie字段确保获取成功")
    print("   5. 保存Cookie到文件便于后续使用")
    print("=" * 80)
