#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
DrissionPage新建标签页测试：验证不会影响现有标签页
功能：测试新建标签页功能，确保不会顶掉现有标签页
重点：学习如何安全地新建标签页进行操作
"""

from DrissionPage import Chromium
import time

def test_new_tab_creation():
    """
    测试新建标签页功能
    验证不会影响现有标签页
    """
    print("=" * 80)
    print("📚 DrissionPage新建标签页测试")
    print("🎯 目标：验证新建标签页不会影响现有标签页")
    print("=" * 80)
    
    try:
        # 步骤1：连接到浏览器
        print("🚀 第一步：连接到浏览器")
        browser = Chromium(9222)
        print("✅ 已连接到浏览器")
        
        # 步骤2：检查现有标签页数量
        print("\n🚀 第二步：检查现有标签页")
        try:
            existing_tabs = browser.get_tabs()
            existing_count = len(existing_tabs)
            print(f"📊 当前浏览器中有 {existing_count} 个标签页")
            
            if existing_count > 0:
                print("📋 现有标签页信息：")
                for i, tab in enumerate(existing_tabs):
                    try:
                        title = tab.title if tab.states.is_alive else "已关闭"
                        url = tab.url if tab.states.is_alive else "无URL"
                        print(f"   标签页 {i+1}: {title} - {url[:50]}...")
                    except:
                        print(f"   标签页 {i+1}: 无法获取信息")
            else:
                print("📋 当前没有打开的标签页")
        except Exception as e:
            print(f"⚠️ 获取现有标签页信息时出错：{e}")
            existing_count = 0
        
        # 步骤3：新建独立标签页
        print("\n🚀 第三步：新建独立标签页")
        print("📄 正在新建独立的测试标签页...")
        
        # 总是新建标签页，不使用现有的
        new_tab = browser.new_tab()
        print("✅ 新建标签页成功！")
        
        # 激活新标签页
        new_tab.set.activate()
        print("🎯 新标签页已激活")
        
        # 步骤4：在新标签页中进行操作
        print("\n🚀 第四步：在新标签页中进行测试操作")
        test_url = "https://www.baidu.com"
        print(f"🌐 在新标签页中访问：{test_url}")
        
        success = new_tab.get(test_url)
        if success:
            print("✅ 页面访问成功")
            new_tab.wait.doc_loaded()
            print(f"📄 页面标题：{new_tab.title}")
            print(f"🔗 页面URL：{new_tab.url}")
        else:
            print("❌ 页面访问失败")
        
        # 步骤5：验证现有标签页未受影响
        print("\n🚀 第五步：验证现有标签页未受影响")
        try:
            current_tabs = browser.get_tabs()
            current_count = len(current_tabs)
            print(f"📊 操作后浏览器中有 {current_count} 个标签页")
            
            if current_count == existing_count + 1:
                print("✅ 标签页数量正确增加了1个")
            else:
                print(f"⚠️ 标签页数量变化异常：预期 {existing_count + 1}，实际 {current_count}")
            
            print("📋 当前所有标签页信息：")
            for i, tab in enumerate(current_tabs):
                try:
                    title = tab.title if tab.states.is_alive else "已关闭"
                    url = tab.url if tab.states.is_alive else "无URL"
                    is_new = tab == new_tab
                    status = "【新建】" if is_new else "【原有】"
                    print(f"   标签页 {i+1} {status}: {title} - {url[:50]}...")
                except:
                    print(f"   标签页 {i+1}: 无法获取信息")
                    
        except Exception as e:
            print(f"⚠️ 验证标签页时出错：{e}")
        
        # 步骤6：询问是否关闭新建的标签页
        print("\n🚀 第六步：清理测试标签页")
        choice = input("是否关闭新建的测试标签页？(y/n): ").lower().strip()
        
        if choice == 'y' or choice == 'yes':
            try:
                new_tab.close()
                print("✅ 测试标签页已关闭")
                
                # 验证关闭后的标签页数量
                final_tabs = browser.get_tabs()
                final_count = len(final_tabs)
                print(f"📊 关闭后浏览器中有 {final_count} 个标签页")
                
                if final_count == existing_count:
                    print("✅ 标签页数量恢复到原始状态")
                else:
                    print(f"⚠️ 标签页数量异常：预期 {existing_count}，实际 {final_count}")
                    
            except Exception as e:
                print(f"❌ 关闭标签页时出错：{e}")
        else:
            print("📄 保留测试标签页")
        
        print("\n🎉 新建标签页测试完成！")
        print("📋 测试总结：")
        print("   ✅ 成功新建了独立的标签页")
        print("   ✅ 新标签页可以正常进行操作")
        print("   ✅ 现有标签页未受到影响")
        print("   ✅ 标签页管理功能正常")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试过程中发生错误：{e}")
        import traceback
        traceback.print_exc()
        return False

def test_cookie_manager_new_tab():
    """
    测试Cookie管理器的新建标签页功能
    """
    print("\n" + "=" * 80)
    print("📚 测试Cookie管理器的新建标签页功能")
    print("=" * 80)
    
    try:
        import sys
        import os
        
        # 添加模块路径
        project_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "..", "实践代码", "===闲鱼商品采集 - 重构")
        sys.path.append(project_path)
        
        from 模块.cookie_manager import CookieManager
        
        print("🚀 正在测试Cookie管理器的新建标签页功能...")
        
        # 初始化Cookie管理器
        cookie_manager = CookieManager()
        config_dir = os.path.join(project_path, "配置")
        cookie_manager.set_config_directory(config_dir)
        
        print("✅ Cookie管理器初始化成功")
        
        # 检查现有标签页
        browser = Chromium(9222)
        existing_tabs = browser.get_tabs()
        existing_count = len(existing_tabs)
        print(f"📊 Cookie获取前浏览器中有 {existing_count} 个标签页")
        
        # 询问是否测试Cookie获取
        choice = input("是否测试Cookie获取功能（会新建标签页）？(y/n): ").lower().strip()
        
        if choice == 'y' or choice == 'yes':
            print("⏳ 开始Cookie获取测试...")
            print("💡 这将新建一个独立的标签页进行Cookie获取")
            
            # 测试Cookie获取（不需要登录）
            new_cookie = cookie_manager.refresh_cookie(need_login=False)
            
            # 检查标签页变化
            current_tabs = browser.get_tabs()
            current_count = len(current_tabs)
            print(f"📊 Cookie获取后浏览器中有 {current_count} 个标签页")
            
            if current_count > existing_count:
                print("✅ Cookie获取过程新建了标签页，现有标签页未受影响")
            else:
                print("ℹ️ 标签页数量未变化")
            
            if new_cookie:
                print("✅ Cookie获取成功")
                print(f"Cookie长度：{len(new_cookie)} 字符")
            else:
                print("❌ Cookie获取失败")
        else:
            print("📚 跳过Cookie获取测试")
        
    except Exception as e:
        print(f"❌ Cookie管理器测试失败：{e}")

if __name__ == "__main__":
    """
    主程序：执行新建标签页测试
    """
    print("=" * 80)
    print("📚 DrissionPage新建标签页完整测试")
    print("🎯 测试目标：验证新建标签页功能不会影响现有标签页")
    print("=" * 80)
    
    try:
        # 测试1：基础新建标签页功能
        print("🚀 开始基础新建标签页测试")
        basic_result = test_new_tab_creation()
        
        if basic_result:
            print("\n✅ 基础新建标签页测试通过！")
            
            # 测试2：Cookie管理器新建标签页功能
            print("\n🚀 开始Cookie管理器新建标签页测试")
            test_cookie_manager_new_tab()
        else:
            print("\n❌ 基础新建标签页测试失败")
        
    except Exception as e:
        print(f"❌ 测试程序执行失败：{e}")
        print("💡 请检查：")
        print("   1. DrissionPage库是否正确安装")
        print("   2. 浏览器是否可以正常启动")
        print("   3. 网络连接是否正常")
    
    print("\n" + "=" * 80)
    print("📚 新建标签页测试完成")
    print("💡 重点回顾：")
    print("   1. 使用browser.new_tab()总是新建标签页")
    print("   2. 不使用browser.latest_tab避免影响现有标签页")
    print("   3. 新建的标签页可以独立进行操作")
    print("   4. 操作完成后可以选择关闭新建的标签页")
    print("=" * 80)
