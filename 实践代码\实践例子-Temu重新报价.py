#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Temu商品数据批量提取GUI版本
功能：
1. 新建标签页访问指定网站
2. 监听认证菜单API获取完整认证信息
3. 查询待处理商品总数并计算页数
4. 批量提取商品数据（orderId、属性集、skcId、skuId）
"""

from DrissionPage import Chromium
import json
import time
import math
from datetime import datetime
import tkinter as tk
from tkinter import ttk, scrolledtext, messagebox
import threading

class TemuDataExtractorGUI:
    """
    Temu商品数据提取GUI界面类
    提供图形化界面来执行商品数据提取操作
    """

    def __init__(self):
        """初始化GUI界面"""
        self.root = tk.Tk()
        self.root.title("Temu商品数据批量提取工具")
        self.root.geometry("800x600")

        # 设置全局字体为微软雅黑
        self.font_family = "Microsoft YaHei"
        # 配置默认字体
        default_font = (self.font_family, 10)
        self.root.option_add("*Font", default_font)

        # 初始化变量
        self.tab = None
        self.auth_headers = None
        self.page_info = None
        self.is_running = False

        self.setup_ui()

    def setup_ui(self):
        """设置用户界面"""
        # 主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        # 配置网格权重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        main_frame.rowconfigure(2, weight=1)

        # 标题
        title_label = ttk.Label(main_frame, text="Temu商品数据批量提取工具",
                               font=(self.font_family, 14, "bold"))
        title_label.grid(row=0, column=0, columnspan=2, pady=(0, 20))

        # 控制按钮框架
        button_frame = ttk.Frame(main_frame)
        button_frame.grid(row=1, column=0, columnspan=2, pady=(0, 10), sticky=(tk.W, tk.E))

        # 开始按钮
        self.start_button = ttk.Button(button_frame, text="开始提取数据",
                                      command=self.start_extraction)
        self.start_button.pack(side=tk.LEFT, padx=(0, 10))

        # 停止按钮
        self.stop_button = ttk.Button(button_frame, text="停止操作",
                                     command=self.stop_extraction, state=tk.DISABLED)
        self.stop_button.pack(side=tk.LEFT, padx=(0, 10))

        # 清空日志按钮
        self.clear_button = ttk.Button(button_frame, text="清空日志",
                                      command=self.clear_log)
        self.clear_button.pack(side=tk.LEFT)

        # 日志显示区域
        log_label = ttk.Label(main_frame, text="操作日志:", font=(self.font_family, 10))
        log_label.grid(row=2, column=0, sticky=tk.W, pady=(0, 5))

        # 创建日志文本框
        self.log_text = scrolledtext.ScrolledText(main_frame, width=80, height=25,
                                                 font=(self.font_family, 9))
        self.log_text.grid(row=3, column=0, columnspan=2, sticky=(tk.W, tk.E, tk.N, tk.S))

        # 状态栏
        self.status_var = tk.StringVar()
        self.status_var.set("就绪")
        status_label = ttk.Label(main_frame, textvariable=self.status_var,
                                font=(self.font_family, 9))
        status_label.grid(row=4, column=0, columnspan=2, sticky=tk.W, pady=(10, 0))

    def log_message(self, message):
        """在日志区域添加消息"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        formatted_message = f"[{timestamp}] {message}\n"

        # 在主线程中更新GUI
        self.root.after(0, self._update_log, formatted_message)

    def _update_log(self, message):
        """更新日志显示（在主线程中执行）"""
        self.log_text.insert(tk.END, message)
        self.log_text.see(tk.END)
        self.root.update_idletasks()

    def update_status(self, status):
        """更新状态栏"""
        self.root.after(0, lambda: self.status_var.set(status))

    def start_extraction(self):
        """开始数据提取"""
        if self.is_running:
            return

        self.is_running = True
        self.start_button.config(state=tk.DISABLED)
        self.stop_button.config(state=tk.NORMAL)

        # 在新线程中执行提取操作
        extraction_thread = threading.Thread(target=self.run_extraction)
        extraction_thread.daemon = True
        extraction_thread.start()

    def stop_extraction(self):
        """停止数据提取"""
        self.is_running = False
        self.start_button.config(state=tk.NORMAL)
        self.stop_button.config(state=tk.DISABLED)
        self.update_status("已停止")
        self.log_message("用户停止了操作")

    def clear_log(self):
        """清空日志"""
        self.log_text.delete(1.0, tk.END)

    def run_extraction(self):
        """执行完整的数据提取流程"""
        try:
            self.update_status("正在运行...")
            self.log_message("开始Temu商品数据批量提取")
            self.log_message("功能：监听认证菜单API → 查询总数 → 批量提取商品数据")

            # 步骤1：创建新标签页并监听认证菜单API
            if not self.is_running:
                return

            self.log_message("第一步：创建新标签页并监听认证菜单API")
            self.tab = self.create_new_tab_and_monitor()

            if self.tab and self.is_running:
                self.log_message(f"标签页创建成功！当前页面：{self.tab.title}")

                # 步骤2：捕获认证信息
                if not self.is_running:
                    return

                self.log_message("第二步：捕获认证菜单API信息")
                self.auth_headers = self.capture_auth_info(self.tab)

                if self.auth_headers and self.is_running:
                    self.log_message("认证信息获取成功！")

                    # 步骤3：查询待处理商品总数
                    if not self.is_running:
                        return

                    self.log_message("第三步：查询待处理商品总数")
                    self.page_info = self.query_todo_count(self.tab, self.auth_headers)

                    if self.page_info and self.is_running:
                        self.log_message("商品总数查询成功！")

                        # 步骤4：批量提取商品数据
                        if not self.is_running:
                            return

                        self.log_message("第四步：批量提取商品数据")
                        self.extract_product_data(self.tab, self.auth_headers, self.page_info)

                        if self.is_running:
                            self.log_message("商品数据提取完成！")
                            self.update_status("提取完成")
                    else:
                        self.log_message("无法获取商品总数信息")
                        self.update_status("获取总数失败")
                else:
                    self.log_message("无法获取认证信息")
                    self.update_status("认证失败")

                # 停止监听器
                if self.tab:
                    self.tab.listen.stop()
                    self.log_message("网络监听器已停止")
            else:
                self.log_message("标签页创建失败")
                self.update_status("创建标签页失败")

            if self.is_running:
                self.log_message("操作总结：")
                self.log_message("   新建标签页并访问目标网站")
                self.log_message("   监听并获取完整认证菜单信息")
                self.log_message("   查询待处理商品总数并计算页数")
                self.log_message("   批量提取商品数据（orderId、属性集、skcId、skuId）")
                self.log_message("   在日志中逐行显示提取的数据")

        except Exception as e:
            self.log_message(f"程序执行过程中发生错误：{e}")
            self.log_message("请检查：")
            self.log_message("   1. 浏览器是否正确启动（端口9222）")
            self.log_message("   2. 网络连接是否正常")
            self.log_message("   3. 目标网址是否可访问")
            self.log_message("   4. 是否需要登录认证")
            self.update_status("执行出错")

        finally:
            # 重置按钮状态
            if self.is_running:
                self.is_running = False
                self.root.after(0, lambda: self.start_button.config(state=tk.NORMAL))
                self.root.after(0, lambda: self.stop_button.config(state=tk.DISABLED))
                if self.status_var.get() == "正在运行...":
                    self.update_status("就绪")

    def create_new_tab_and_monitor(self):
        """
        创建新标签页并开始监听认证菜单API
        """
        self.log_message("开始创建新标签页并监听认证菜单API...")

        # 连接到9222端口的浏览器
        browser = Chromium(9222)
        self.log_message("已连接到浏览器（端口9222）")

        # 新建标签页
        new_tab = browser.new_tab()
        self.log_message("新建空白标签页完成")

        # 激活标签页
        new_tab.set.activate()
        self.log_message("标签页已激活")

        # 启动网络监听器 - 监听认证API
        target_api = "https://agentseller.temu.com/api/seller/auth/menu"
        self.log_message("启动网络监听器...")
        self.log_message(f"监听目标API: {target_api}")
        new_tab.listen.start(targets="api/seller/auth/menu")
        self.log_message("网络监听器已启动，开始监听认证菜单API")

        # 访问目标页面
        target_url = "https://agentseller.temu.com/newon/product-select"
        self.log_message(f"正在访问目标页面：{target_url}")

        # 访问页面
        new_tab.get(target_url)

        self.log_message("等待页面加载...")
        new_tab.wait.load_start()
        new_tab.wait.doc_loaded()
        self.log_message("页面加载完成")

        return new_tab

    def capture_auth_info(self, tab):
        """
        捕获认证菜单API请求并提取认证信息
        """
        self.log_message("=" * 50)
        self.log_message("开始监听并捕获认证菜单API请求")
        self.log_message("=" * 50)

        self.log_message("等待认证菜单API请求...")
        self.log_message("提示：如果页面已加载但未捕获到请求，请尝试刷新页面或进行操作")

        try:
            # 等待认证菜单API请求，设置较长的超时时间
            packet = tab.listen.wait(timeout=60)
            if not packet:
                self.log_message("未捕获到认证菜单API请求")
                return None
        except Exception as e:
            self.log_message(f"等待认证菜单API请求时发生错误：{e}")
            return None

        self.log_message("成功捕获到认证菜单API请求！")

        # 提取重要的认证头信息
        auth_headers = {}
        important_headers = [
            'Anti-Content', 'mallid', 'cookie', 'authorization',
            'x-csrf-token', 'x-requested-with', 'user-agent',
            'accept', 'accept-language', 'content-type',
            'origin', 'referer', 'sec-fetch-dest', 'sec-fetch-mode', 'sec-fetch-site'
        ]

        self.log_message("正在提取认证头信息...")
        if packet.request and packet.request.headers:
            for key, value in packet.request.headers.items():
                # 检查是否是重要的认证头
                if any(important_header.lower() == key.lower() for important_header in important_headers):
                    clean_key = key.strip()
                    clean_value = str(value).strip()
                    if clean_key and clean_value:
                        auth_headers[clean_key] = clean_value

        self.log_message(f"成功提取到 {len(auth_headers)} 个认证头信息")

        # 显示提取到的关键认证信息（不显示敏感内容）
        self.log_message("提取到的认证头类型：")
        for key in auth_headers.keys():
            if key.lower() in ['cookie', 'authorization']:
                self.log_message(f"   {key}: [已获取，长度{len(auth_headers[key])}字符]")
            else:
                self.log_message(f"   {key}: {auth_headers[key][:50]}...")

        return auth_headers

    def query_todo_count(self, tab, auth_headers):
        """
        查询待处理商品总数
        """
        self.log_message("=" * 50)
        self.log_message("查询待处理商品总数")
        self.log_message("=" * 50)

        if not auth_headers:
            self.log_message("没有认证信息，无法发起请求")
            return None

        api_url = "https://agentseller.temu.com/api/kiana/mms/robin/querySupplierTodoCount"
        request_data = {}

        self.log_message(f"API地址：{api_url}")
        self.log_message(f"请求数据：{json.dumps(request_data, ensure_ascii=False)}")

        try:
            self.log_message("正在查询待处理商品总数...")

            # 使用JavaScript发起请求
            js_code = f'''
            (async function() {{
                try {{
                    const response = await fetch('{api_url}', {{
                        method: 'POST',
                        headers: {json.dumps(auth_headers)},
                        body: JSON.stringify({json.dumps(request_data)})
                    }});

                    const responseText = await response.text();

                    const result = {{
                        status: response.status,
                        statusText: response.statusText,
                        body: responseText,
                        success: true
                    }};

                    window.todoCountResponse = result;
                    return result;
                }} catch (error) {{
                    const errorResult = {{
                        error: error.message,
                        success: false
                    }};
                    window.todoCountResponse = errorResult;
                    return errorResult;
                }}
            }})();
            '''

            # 执行JavaScript代码
            tab.run_js('window.todoCountResponse = null;')
            tab.run_js(js_code)

            # 等待请求完成
            max_wait_time = 10
            wait_interval = 0.5

            for _ in range(int(max_wait_time / wait_interval)):
                time.sleep(wait_interval)
                try:
                    response_data = tab.run_js('return window.todoCountResponse;')
                    if response_data is not None:
                        break
                except Exception:
                    pass
            else:
                self.log_message("请求超时")
                return None

            if response_data and response_data.get('success', True):
                self.log_message("查询待处理商品总数成功！")

                try:
                    # 解析响应JSON
                    response_json = json.loads(response_data['body'])

                    # 提取总数
                    total_count = response_json.get('result', {}).get('total', 0)
                    self.log_message(f"待处理商品总数：{total_count} 条")

                    # 计算页数（每页50条）
                    page_size = 50
                    total_pages = math.ceil(total_count / page_size)
                    self.log_message(f"每页 {page_size} 条，共需处理 {total_pages} 页")

                    return {
                        'total_count': total_count,
                        'page_size': page_size,
                        'total_pages': total_pages
                    }

                except Exception as e:
                    self.log_message(f"解析响应数据失败：{e}")
                    self.log_message(f"原始响应：{response_data['body']}")
                    return None
            else:
                self.log_message("查询待处理商品总数失败")
                if 'error' in response_data:
                    self.log_message(f"错误信息：{response_data['error']}")
                return None

        except Exception as e:
            self.log_message(f"发起请求时发生错误：{e}")
            return None

    def extract_product_data(self, tab, auth_headers, page_info):
        """
        批量提取商品数据
        """
        self.log_message("=" * 50)
        self.log_message("开始批量提取商品数据")
        self.log_message("=" * 50)

        if not auth_headers or not page_info:
            self.log_message("缺少必要信息，无法提取数据")
            return

        api_url = "https://agentseller.temu.com/api/kiana/mms/robin/searchForChainSupplier"
        total_pages = page_info['total_pages']
        page_size = page_info['page_size']

        self.log_message(f"准备提取 {total_pages} 页数据，每页 {page_size} 条")
        self.log_message("=" * 50)

        # 测试模式：只提取第1页数据
        test_pages = 1
        self.log_message(f"测试模式：只提取第 {test_pages} 页数据")

        # 初始化订单收集列表
        self.collected_orders = []

        for page_num in range(1, test_pages + 1):
            if not self.is_running:  # 检查是否需要停止
                self.log_message("用户停止了操作，中断数据提取")
                break

            self.log_message(f"正在提取第 {page_num} 页数据...")

            request_data = {
                "pageSize": page_size,
                "pageNum": page_num,
                "supplierTodoTypeList": [6, 1]
            }

            try:
                # 使用JavaScript发起请求
                js_code = f'''
                (async function() {{
                    try {{
                        const response = await fetch('{api_url}', {{
                            method: 'POST',
                            headers: {json.dumps(auth_headers)},
                            body: JSON.stringify({json.dumps(request_data)})
                        }});

                        const responseText = await response.text();

                        const result = {{
                            status: response.status,
                            statusText: response.statusText,
                            body: responseText,
                            success: true
                        }};

                        window.productDataResponse = result;
                        return result;
                    }} catch (error) {{
                        const errorResult = {{
                            error: error.message,
                            success: false
                        }};
                        window.productDataResponse = errorResult;
                        return errorResult;
                    }}
                }})();
                '''

                # 执行JavaScript代码
                tab.run_js('window.productDataResponse = null;')
                tab.run_js(js_code)

                # 等待请求完成
                max_wait_time = 10
                wait_interval = 0.5

                for _ in range(int(max_wait_time / wait_interval)):
                    if not self.is_running:  # 检查是否需要停止
                        break
                    time.sleep(wait_interval)
                    try:
                        response_data = tab.run_js('return window.productDataResponse;')
                        if response_data is not None:
                            break
                    except Exception:
                        pass
                else:
                    if self.is_running:
                        self.log_message(f"第 {page_num} 页请求超时")
                    continue

                if not self.is_running:
                    break

                if response_data and response_data.get('success', True):
                    self.log_message(f"第 {page_num} 页数据获取成功！")

                    try:
                        # 解析响应JSON
                        response_json = json.loads(response_data['body'])
                        data_list = response_json.get('result', {}).get('dataList', [])

                        self.log_message(f"第 {page_num} 页包含 {len(data_list)} 个商品项")

                        # 提取每个商品的数据
                        for i, data_item in enumerate(data_list):
                            if not self.is_running:  # 检查是否需要停止
                                break

                            try:
                                skc_list = data_item.get('skcList', [])
                                if not skc_list:
                                    self.log_message(f"第{page_num}页商品{i+1}: 没有skcList数据")
                                    continue

                                for skc_index, skc_item in enumerate(skc_list):
                                    if not self.is_running:  # 检查是否需要停止
                                        break

                                    try:
                                        # 提取skcId
                                        skc_id = skc_item.get('skcId', 'N/A')

                                        # 提取skuId
                                        sku_list = skc_item.get('skuList', [])
                                        sku_id = sku_list[0].get('skuId', 'N/A') if sku_list else 'N/A'

                                        # 提取orderId和属性集
                                        supplier_price_review_list = skc_item.get('supplierPriceReviewInfoList', [])
                                        if supplier_price_review_list:
                                            price_review_info = supplier_price_review_list[0]
                                            order_id = price_review_info.get('priceOrderId', 'N/A')

                                            # 提取属性集
                                            product_sku_list = price_review_info.get('productSkuList', [])
                                            if product_sku_list:
                                                product_property_list = product_sku_list[0].get('productPropertyList', [])
                                                if product_property_list:
                                                    property_value = product_property_list[0].get('value', 'N/A')
                                                else:
                                                    property_value = 'N/A'
                                            else:
                                                property_value = 'N/A'
                                        else:
                                            order_id = 'N/A'
                                            property_value = 'N/A'

                                        # 记录提取的数据 - 使用清晰易懂的格式
                                        self.log_message(f"[第{page_num}页] 订单ID: {order_id} | 属性: {property_value} | SKC编号: {skc_id} | SKU编号: {sku_id}")

                                        # 收集订单ID用于后续批量查询价格
                                        if order_id and order_id != 'N/A':
                                            self.collected_orders.append({
                                                'order_id': order_id,
                                                'page_num': page_num,
                                                'property_value': property_value,
                                                'skc_id': skc_id,
                                                'sku_id': sku_id
                                            })

                                    except Exception as skc_error:
                                        self.log_message(f"第{page_num}页商品{i+1}的SKC{skc_index+1}数据解析出错：{skc_error}")
                                        continue

                            except Exception as e:
                                self.log_message(f"解析第 {page_num} 页商品 {i+1} 数据时出错：{e}")
                                continue

                    except Exception as e:
                        self.log_message(f"解析第 {page_num} 页响应数据失败：{e}")
                        continue
                else:
                    self.log_message(f"第 {page_num} 页数据获取失败")
                    if 'error' in response_data:
                        self.log_message(f"错误信息：{response_data['error']}")
                    continue

            except Exception as e:
                self.log_message(f"第 {page_num} 页请求时发生错误：{e}")
                continue

            # 页面间隔，避免请求过快
            if page_num < test_pages and self.is_running:
                self.log_message("等待1秒后继续下一页...")
                time.sleep(1)

        # 批量查询价格信息
        if self.collected_orders and self.is_running:
            self.log_message("=" * 50)
            self.log_message("开始批量查询价格信息")
            self.log_message("=" * 50)
            self.log_message(f"共收集到 {len(self.collected_orders)} 个订单，开始逐个查询价格...")

            for i, order_info in enumerate(self.collected_orders):
                if not self.is_running:  # 检查是否需要停止
                    self.log_message("用户停止了操作，中断价格查询")
                    break

                order_id = order_info['order_id']
                self.log_message(f"[{i+1}/{len(self.collected_orders)}] 查询订单 {order_id} 的价格信息...")

                price_info = self.query_price_info(tab, auth_headers, order_id)
                if price_info:
                    self.log_message(f"订单 {order_id} 价格查询完成")
                else:
                    self.log_message(f"订单 {order_id} 价格查询失败")

                # 添加间隔，避免请求过快
                if self.is_running and i < len(self.collected_orders) - 1:
                    time.sleep(0.5)

            if self.is_running:
                self.log_message("=" * 50)
                self.log_message("批量价格查询完成")
                self.log_message("=" * 50)

    def query_price_info(self, tab, auth_headers, order_id):
        """
        查询订单的价格信息
        """
        if not auth_headers or not order_id or order_id == 'N/A':
            self.log_message(f"订单ID {order_id} 无效，跳过价格查询")
            return None

        api_url = "https://agentseller.temu.com/api/kiana/mms/magneto/api/price-review-order/no-bom/reject-remark"
        request_data = {"orderId": int(order_id)}

        try:
            # 使用JavaScript发起请求
            js_code = f'''
            (async function() {{
                try {{
                    const response = await fetch('{api_url}', {{
                        method: 'POST',
                        headers: {json.dumps(auth_headers)},
                        body: JSON.stringify({json.dumps(request_data)})
                    }});

                    const responseText = await response.text();

                    const result = {{
                        status: response.status,
                        statusText: response.statusText,
                        body: responseText,
                        success: true
                    }};

                    window.priceInfoResponse = result;
                    return result;
                }} catch (error) {{
                    const errorResult = {{
                        error: error.message,
                        success: false
                    }};
                    window.priceInfoResponse = errorResult;
                    return errorResult;
                }}
            }})();
            '''

            # 执行JavaScript代码
            tab.run_js('window.priceInfoResponse = null;')
            tab.run_js(js_code)

            # 等待请求完成
            max_wait_time = 10
            wait_interval = 0.5

            for _ in range(int(max_wait_time / wait_interval)):
                if not self.is_running:  # 检查是否需要停止
                    break
                time.sleep(wait_interval)
                try:
                    response_data = tab.run_js('return window.priceInfoResponse;')
                    if response_data is not None:
                        break
                except Exception:
                    pass
            else:
                if self.is_running:
                    self.log_message(f"订单 {order_id} 价格查询请求超时")
                return None

            if not self.is_running:
                return None

            if response_data and response_data.get('success', True):
                try:
                    # 解析响应JSON
                    response_json = json.loads(response_data['body'])

                    if response_json.get('success', False):
                        result = response_json.get('result', {})

                        # 提取价格信息
                        supply_price = result.get('supplyPrice', 0)
                        suggest_supply_price = result.get('suggestSupplyPrice', 0)
                        price_currency = result.get('priceCurrency', 'CNY')
                        suggest_price_currency = result.get('suggestPriceCurrency', 'CNY')

                        # 转换为元（假设原始数据是分）
                        original_price = supply_price / 100 if supply_price else 0
                        suggest_price = suggest_supply_price / 100 if suggest_supply_price else 0

                        # 格式化价格显示
                        original_price_str = f"¥{original_price:.2f}"
                        suggest_price_str = f"¥{suggest_price:.2f}"

                        # 记录价格信息
                        price_info = f"订单ID: {order_id} | 原申报价格({price_currency}): {original_price_str} | 参考申报价格({suggest_price_currency}): {suggest_price_str}"
                        self.log_message(price_info)

                        # 同时在控制台打印
                        print(price_info)

                        return {
                            'order_id': order_id,
                            'original_price': original_price,
                            'suggest_price': suggest_price,
                            'price_currency': price_currency,
                            'suggest_price_currency': suggest_price_currency,
                            'original_price_str': original_price_str,
                            'suggest_price_str': suggest_price_str
                        }
                    else:
                        error_msg = response_json.get('errorMsg', '未知错误')
                        self.log_message(f"订单 {order_id} 价格查询失败：{error_msg}")
                        return None

                except Exception as e:
                    self.log_message(f"订单 {order_id} 价格信息解析失败：{e}")
                    self.log_message(f"原始响应：{response_data['body']}")
                    return None
            else:
                self.log_message(f"订单 {order_id} 价格查询请求失败")
                if 'error' in response_data:
                    self.log_message(f"错误信息：{response_data['error']}")
                return None

        except Exception as e:
            self.log_message(f"订单 {order_id} 价格查询时发生错误：{e}")
            return None

    def run(self):
        """启动GUI应用程序"""
        self.root.mainloop()

if __name__ == "__main__":
    """
    主程序：启动GUI界面
    """
    try:
        # 创建并启动GUI应用程序
        app = TemuDataExtractorGUI()
        app.run()
    except Exception as e:
        # 如果GUI启动失败，显示错误信息
        import tkinter.messagebox as msgbox
        msgbox.showerror("启动错误", f"GUI启动失败：{e}\n\n请检查：\n1. 是否安装了tkinter库\n2. 系统是否支持图形界面")
        print(f"GUI启动失败：{e}")
        print("请检查：")
        print("1. 是否安装了tkinter库")
        print("2. 系统是否支持图形界面")
