from DrissionPage import Chromium
import json
import time
import os
from datetime import datetime

def create_save_directory():
    save_dir = "订单管理API响应数据"
    if not os.path.exists(save_dir):
        os.makedirs(save_dir)
    return save_dir

def save_response_to_file(data, filename_prefix="订单API响应"):
    save_dir = create_save_directory()
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    filename = f"{filename_prefix}_{timestamp}.json"
    filepath = os.path.join(save_dir, filename)

    try:
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(data, f, indent=4, ensure_ascii=False)
        return filepath
    except Exception:
        return None

def open_target_page_and_monitor():
    browser = Chromium(9222)
    new_tab = browser.new_tab()
    new_tab.set.activate()

    target_api = "api/seller/auth/menu"
    new_tab.listen.start(targets=target_api)

    target_url = "https://agentseller.temu.com/stock/fully-mgt/order-manage-urgency"
    new_tab.get(target_url)

    try:
        packet = new_tab.listen.wait(timeout=3)
        if packet:
            return new_tab, packet
    except:
        pass

    new_tab.wait.load_start()

    try:
        packet = new_tab.listen.wait(timeout=2)
        if packet:
            return new_tab, packet
    except:
        pass

    new_tab.wait.doc_loaded()
    return new_tab, None

def capture_api_request(tab):
    try:
        packet = tab.listen.wait(timeout=3)
        if packet:
            return packet

        packet = tab.listen.wait(timeout=5)
        if packet:
            return packet

        packet = tab.listen.wait(timeout=10)
        if packet:
            return packet

        return None
    except Exception:
        return None

def display_captured_api_info(packet):
    return packet

def extract_auth_headers(packet):
    if not packet or not packet.request:
        return None

    auth_headers = {}
    important_headers = [
        'Anti-Content', 'mallid', 'cookie', 'authorization',
        'x-csrf-token', 'x-requested-with', 'user-agent',
        'accept', 'accept-language', 'content-type',
        'origin', 'referer'
    ]

    if packet.request.headers:
        for key, value in packet.request.headers.items():
            if any(important_header.lower() == key.lower() for important_header in important_headers):
                clean_key = key.strip()
                clean_value = str(value).strip()
                if clean_key and clean_value:
                    auth_headers[clean_key] = clean_value

    auth_data = {
        'extraction_time': datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
        'original_url': packet.url,
        'method': packet.method,
        'headers': auth_headers,
        'original_post_data': packet.request.postData if packet.request.postData else None,
        'description': '从真实API请求中提取的重要认证信息'
    }

    save_response_to_file(auth_data, "提取的认证信息")
    return auth_headers

def send_custom_api_request(tab, auth_headers):
    if not auth_headers:
        return None

    custom_request_data = {
        "pageNo": 1,
        "pageSize": 100,
        "urgencyType": 1,
        "isCustomGoods": False,
        "statusList": [1],
        "oneDimensionSort": {
            "firstOrderByParam": "expectLatestDeliverTime",
            "firstOrderByDesc": 0
        }
    }

    api_url = "https://agentseller.temu.com/mms/venom/api/supplier/purchase/manager/querySubOrderList"

    try:
        try:
            tab.url
        except Exception:
            pass

        js_code = f'''
        (async function() {{
            try {{
                const response = await fetch('{api_url}', {{
                    method: 'POST',
                    headers: {json.dumps(auth_headers)},
                    body: JSON.stringify({json.dumps(custom_request_data)})
                }});

                const responseText = await response.text();

                const result = {{
                    status: response.status,
                    statusText: response.statusText,
                    body: responseText,
                    success: true
                }};

                window.customOrderApiResponse = result;
                return result;
            }} catch (error) {{
                const errorResult = {{
                    error: error.message,
                    success: false
                }};
                window.customOrderApiResponse = errorResult;
                return errorResult;
            }}
        }})();
        '''

        tab.run_js('window.customOrderApiResponse = null;')
        tab.run_js(js_code)

        max_wait_time = 10
        wait_interval = 0.5

        for _ in range(int(max_wait_time / wait_interval)):
            time.sleep(wait_interval)
            try:
                response_data = tab.run_js('return window.customOrderApiResponse;')
                if response_data is not None:
                    break
            except Exception:
                pass
        else:
            return None

        if response_data:
            if not response_data.get('success', True) or 'error' in response_data:
                return None

            try:
                response_json = json.loads(response_data['body'])

                complete_response_data = {
                    'request_time': datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                    'api_url': api_url,
                    'request_method': 'POST',
                    'request_data': custom_request_data,
                    'response_status': response_data.get('status'),
                    'response_status_text': response_data.get('statusText'),
                    'response_headers': response_data.get('headers', {}),
                    'response_body': response_json,
                    'description': '订单管理API的完整响应信息'
                }

                save_response_to_file(complete_response_data, "订单管理API完整响应")

            except Exception as e:
                raw_response_data = {
                    'request_time': datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                    'api_url': api_url,
                    'request_data': custom_request_data,
                    'response_status': response_data.get('status'),
                    'response_body_raw': response_data['body'],
                    'json_parse_error': str(e),
                    'description': '订单管理API原始响应（JSON解析失败）'
                }

                save_response_to_file(raw_response_data, "订单管理API原始响应")

            return response_data
        else:
            return None

    except Exception:
        return None

if __name__ == "__main__":
    try:
        result = open_target_page_and_monitor()

        if result[0]:
            tab = result[0]
            early_packet = result[1]

            if early_packet:
                packet = early_packet
                display_captured_api_info(packet)
            else:
                packet = capture_api_request(tab)
                if packet:
                    display_captured_api_info(packet)

            if packet:
                auth_headers = extract_auth_headers(packet)

                if auth_headers:
                    custom_response = send_custom_api_request(tab, auth_headers)

            tab.listen.stop()

    except Exception:
        pass
