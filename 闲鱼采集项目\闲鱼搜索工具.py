#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
闲鱼采集项目 - 搜索工具
功能：通过GUI界面搜索指定内容
目标网站：https://www.goofish.com/
"""

import tkinter as tk
from tkinter import ttk, messagebox, scrolledtext
from DrissionPage import Chromium
import threading
import time
import urllib.parse

class XianYuSearchTool:
    """
    闲鱼搜索工具类
    提供GUI界面进行闲鱼商品搜索
    """
    
    def __init__(self):
        """
        初始化GUI界面和浏览器对象
        """
        # 创建主窗口
        self.root = tk.Tk()
        self.root.title("闲鱼搜索工具")
        self.root.geometry("800x600")
        
        # 设置全局字体为微软雅黑
        self.root.option_add("*Font", "微软雅黑 10")
        
        # 浏览器对象
        self.browser = None
        self.current_tab = None
        
        # 创建界面
        self.create_widgets()
        
        print("✅ 闲鱼搜索工具初始化完成")
    
    def create_widgets(self):
        """
        创建GUI界面组件
        """
        # 主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 配置网格权重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        
        # 标题标签
        title_label = ttk.Label(main_frame, text="闲鱼商品搜索工具", 
                               font=("微软雅黑", 16, "bold"))
        title_label.grid(row=0, column=0, columnspan=3, pady=(0, 20))
        
        # 搜索关键词输入区域
        ttk.Label(main_frame, text="搜索关键词:", 
                 font=("微软雅黑", 10)).grid(row=1, column=0, sticky=tk.W, pady=5)
        
        self.search_entry = ttk.Entry(main_frame, width=40, font=("微软雅黑", 10))
        self.search_entry.grid(row=1, column=1, sticky=(tk.W, tk.E), padx=(10, 10), pady=5)
        
        # 搜索按钮
        self.search_button = ttk.Button(main_frame, text="开始搜索", 
                                       command=self.start_search,
                                       style="Accent.TButton")
        self.search_button.grid(row=1, column=2, padx=(0, 0), pady=5)
        
        # 浏览器状态区域
        status_frame = ttk.LabelFrame(main_frame, text="浏览器状态", padding="10")
        status_frame.grid(row=2, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(20, 10))
        status_frame.columnconfigure(1, weight=1)
        
        ttk.Label(status_frame, text="连接状态:", 
                 font=("微软雅黑", 10)).grid(row=0, column=0, sticky=tk.W)
        
        self.status_label = ttk.Label(status_frame, text="未连接", 
                                     font=("微软雅黑", 10))
        self.status_label.grid(row=0, column=1, sticky=tk.W, padx=(10, 0))
        
        # 连接浏览器按钮
        self.connect_button = ttk.Button(status_frame, text="连接浏览器", 
                                        command=self.connect_browser)
        self.connect_button.grid(row=0, column=2, padx=(10, 0))
        
        # 日志输出区域
        log_frame = ttk.LabelFrame(main_frame, text="操作日志", padding="10")
        log_frame.grid(row=3, column=0, columnspan=3, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(10, 0))
        log_frame.columnconfigure(0, weight=1)
        log_frame.rowconfigure(0, weight=1)
        main_frame.rowconfigure(3, weight=1)
        
        # 创建滚动文本框
        self.log_text = scrolledtext.ScrolledText(log_frame, 
                                                 width=80, height=20,
                                                 font=("微软雅黑", 9),
                                                 wrap=tk.WORD)
        self.log_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 清空日志按钮
        clear_button = ttk.Button(log_frame, text="清空日志", 
                                 command=self.clear_log)
        clear_button.grid(row=1, column=0, pady=(10, 0))
        
        print("✅ GUI界面创建完成")
    
    def log_message(self, message):
        """
        在日志区域显示消息
        """
        timestamp = time.strftime("%H:%M:%S")
        log_entry = f"[{timestamp}] {message}\n"
        
        # 在主线程中更新GUI
        self.root.after(0, lambda: self._update_log(log_entry))
        
        # 同时在控制台输出
        print(message)
    
    def _update_log(self, log_entry):
        """
        更新日志文本框（在主线程中执行）
        """
        self.log_text.insert(tk.END, log_entry)
        self.log_text.see(tk.END)  # 自动滚动到最新内容
    
    def clear_log(self):
        """
        清空日志内容
        """
        self.log_text.delete(1.0, tk.END)
        print("📝 日志已清空")
    
    def connect_browser(self):
        """
        连接到浏览器
        """
        def connect_thread():
            try:
                self.log_message("🔄 正在连接浏览器...")
                
                # 连接到9222端口的浏览器
                self.browser = Chromium(9222)
                
                # 更新状态标签
                self.root.after(0, lambda: self.status_label.config(text="已连接"))
                self.root.after(0, lambda: self.connect_button.config(state="disabled"))
                
                self.log_message("✅ 浏览器连接成功")
                self.log_message(f"📊 当前标签页数量: {self.browser.tabs_count}")
                
            except Exception as e:
                self.log_message(f"❌ 浏览器连接失败: {str(e)}")
                messagebox.showerror("连接失败", f"无法连接到浏览器:\n{str(e)}")
        
        # 在新线程中执行连接操作
        threading.Thread(target=connect_thread, daemon=True).start()
    
    def start_search(self):
        """
        开始搜索操作
        """
        # 获取搜索关键词
        search_keyword = self.search_entry.get().strip()
        
        if not search_keyword:
            messagebox.showwarning("输入错误", "请输入搜索关键词")
            return
        
        if not self.browser:
            messagebox.showwarning("连接错误", "请先连接浏览器")
            return
        
        # 禁用搜索按钮防止重复点击
        self.search_button.config(state="disabled")
        
        def search_thread():
            try:
                self.log_message(f"🔍 开始搜索关键词: {search_keyword}")
                
                # 对搜索关键词进行URL编码
                encoded_keyword = urllib.parse.quote(search_keyword)
                
                # 构建搜索URL
                search_url = f"https://www.goofish.com/search?spm=a21ybx.search.searchSuggest.1.2b7b6a7dH9S1MO&q={encoded_keyword}"
                
                self.log_message(f"🌐 搜索URL: {search_url}")
                
                # 创建新标签页
                self.log_message("📄 正在创建新标签页...")
                self.current_tab = self.browser.new_tab()
                
                # 激活标签页
                self.current_tab.set.activate()
                self.log_message("✅ 新标签页已创建并激活")
                
                # 访问搜索页面
                self.log_message("🚀 正在访问闲鱼搜索页面...")
                self.current_tab.get(search_url)
                
                # 等待页面加载
                self.log_message("⏳ 等待页面加载...")
                self.current_tab.wait.doc_loaded()
                
                # 获取页面标题
                page_title = self.current_tab.title
                self.log_message(f"📄 页面标题: {page_title}")
                
                # 检查是否成功加载
                if "闲鱼" in page_title or "goofish" in self.current_tab.url.lower():
                    self.log_message("✅ 搜索页面加载成功")
                    self.log_message(f"🔗 当前页面URL: {self.current_tab.url}")
                    
                    # 等待一下让页面完全加载
                    time.sleep(2)
                    
                    # 尝试获取搜索结果信息
                    self.log_message("🔍 正在分析搜索结果...")
                    self.analyze_search_results()
                    
                else:
                    self.log_message("⚠️ 页面可能未正确加载，请检查网络连接")
                
            except Exception as e:
                self.log_message(f"❌ 搜索过程中发生错误: {str(e)}")
                messagebox.showerror("搜索失败", f"搜索过程中发生错误:\n{str(e)}")
            
            finally:
                # 重新启用搜索按钮
                self.root.after(0, lambda: self.search_button.config(state="normal"))
        
        # 在新线程中执行搜索操作
        threading.Thread(target=search_thread, daemon=True).start()
    
    def analyze_search_results(self):
        """
        分析搜索结果页面
        """
        try:
            self.log_message("📊 开始分析搜索结果页面...")
            
            # 等待页面元素加载
            time.sleep(3)
            
            # 尝试获取页面基本信息
            current_url = self.current_tab.url
            self.log_message(f"🔗 当前页面URL: {current_url}")
            
            # 检查页面是否包含搜索结果
            page_text = self.current_tab.html
            
            if "搜索结果" in page_text or "商品" in page_text:
                self.log_message("✅ 检测到搜索结果页面")
            else:
                self.log_message("⚠️ 未检测到明显的搜索结果标识")
            
            # 尝试获取页面标题和基本信息
            self.log_message(f"📄 页面标题: {self.current_tab.title}")
            self.log_message("✅ 搜索操作完成")
            self.log_message("💡 您现在可以在浏览器中查看搜索结果")
            
        except Exception as e:
            self.log_message(f"⚠️ 分析搜索结果时发生错误: {str(e)}")
    
    def run(self):
        """
        启动GUI应用程序
        """
        self.log_message("🚀 闲鱼搜索工具启动")
        self.log_message("💡 使用说明:")
        self.log_message("   1. 点击'连接浏览器'按钮连接到浏览器")
        self.log_message("   2. 输入要搜索的关键词")
        self.log_message("   3. 点击'开始搜索'按钮进行搜索")
        self.log_message("   4. 程序会自动打开新标签页并访问闲鱼搜索页面")
        
        # 启动GUI主循环
        self.root.mainloop()

if __name__ == "__main__":
    """
    主程序入口
    """
    print("=" * 60)
    print("🛒 闲鱼采集项目 - 搜索工具")
    print("🎯 功能: 通过GUI界面搜索闲鱼商品")
    print("=" * 60)
    
    try:
        # 创建并运行搜索工具
        app = XianYuSearchTool()
        app.run()
        
    except Exception as e:
        print(f"❌ 程序启动失败: {e}")
        print("💡 请检查:")
        print("   1. Python环境是否正确")
        print("   2. DrissionPage库是否已安装")
        print("   3. tkinter库是否可用")
    
    print("\n" + "=" * 60)
    print("👋 闲鱼搜索工具已退出")
    print("=" * 60)
